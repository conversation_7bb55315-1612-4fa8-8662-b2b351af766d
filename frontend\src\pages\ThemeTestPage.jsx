import React from 'react'
import {
  Container,
  Typography,
  Box,
  Card,
  CardContent,
  Button,
  Paper,
  Chip,
  TextField,
  List,
  ListItem,
  ListItemText,
  ListItemButton,
  AppBar,
  Toolbar,
  useTheme
} from '@mui/material'
import { useTheme as useCustomTheme } from '../context/ThemeContext'
import { ThemeToggle } from '../components/ui'

const ThemeTestPage = () => {
  const muiTheme = useTheme()
  const { themeMode, isDark, isLight, isSystem, actualTheme } = useCustomTheme()

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Box sx={{ mb: 4, textAlign: 'center' }}>
        <Typography variant="h3" gutterBottom>
          Theme Test Page
        </Typography>
        <Typography variant="body1" color="text.secondary" gutterBottom>
          Test all theme variations and components
        </Typography>
        <ThemeToggle variant="menu" />
      </Box>

      {/* Theme Information */}
      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Typography variant="h5" gutterBottom>
            Current Theme Information
          </Typography>
          <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: 2 }}>
            <Box>
              <Typography variant="subtitle2">Theme Mode:</Typography>
              <Chip label={themeMode} color="primary" />
            </Box>
            <Box>
              <Typography variant="subtitle2">Actual Theme:</Typography>
              <Chip label={actualTheme} color="secondary" />
            </Box>
            <Box>
              <Typography variant="subtitle2">Is Dark:</Typography>
              <Chip label={isDark ? 'Yes' : 'No'} color={isDark ? 'default' : 'primary'} />
            </Box>
            <Box>
              <Typography variant="subtitle2">Is Light:</Typography>
              <Chip label={isLight ? 'Yes' : 'No'} color={isLight ? 'primary' : 'default'} />
            </Box>
            <Box>
              <Typography variant="subtitle2">Is System:</Typography>
              <Chip label={isSystem ? 'Yes' : 'No'} color={isSystem ? 'warning' : 'default'} />
            </Box>
            <Box>
              <Typography variant="subtitle2">MUI Theme Mode:</Typography>
              <Chip label={muiTheme.palette.mode} color="info" />
            </Box>
          </Box>
        </CardContent>
      </Card>

      {/* Color Palette */}
      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Typography variant="h5" gutterBottom>
            Color Palette
          </Typography>
          <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))', gap: 2 }}>
            <Paper sx={{ p: 2, bgcolor: 'primary.main', color: 'primary.contrastText' }}>
              <Typography variant="subtitle2">Primary</Typography>
              <Typography variant="caption">{muiTheme.palette.primary.main}</Typography>
            </Paper>
            <Paper sx={{ p: 2, bgcolor: 'secondary.main', color: 'secondary.contrastText' }}>
              <Typography variant="subtitle2">Secondary</Typography>
              <Typography variant="caption">{muiTheme.palette.secondary.main}</Typography>
            </Paper>
            <Paper sx={{ p: 2, bgcolor: 'success.main', color: 'success.contrastText' }}>
              <Typography variant="subtitle2">Success</Typography>
              <Typography variant="caption">{muiTheme.palette.success.main}</Typography>
            </Paper>
            <Paper sx={{ p: 2, bgcolor: 'warning.main', color: 'warning.contrastText' }}>
              <Typography variant="subtitle2">Warning</Typography>
              <Typography variant="caption">{muiTheme.palette.warning.main}</Typography>
            </Paper>
            <Paper sx={{ p: 2, bgcolor: 'error.main', color: 'error.contrastText' }}>
              <Typography variant="subtitle2">Error</Typography>
              <Typography variant="caption">{muiTheme.palette.error.main}</Typography>
            </Paper>
            <Paper sx={{ p: 2, bgcolor: 'background.paper', color: 'text.primary', border: 1, borderColor: 'divider' }}>
              <Typography variant="subtitle2">Background</Typography>
              <Typography variant="caption">{muiTheme.palette.background.paper}</Typography>
            </Paper>
          </Box>
        </CardContent>
      </Card>

      {/* Components */}
      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Typography variant="h5" gutterBottom>
            Component Examples
          </Typography>
          
          {/* Buttons */}
          <Box sx={{ mb: 3 }}>
            <Typography variant="h6" gutterBottom>Buttons</Typography>
            <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
              <Button variant="contained" color="primary">Primary</Button>
              <Button variant="contained" color="secondary">Secondary</Button>
              <Button variant="outlined" color="primary">Outlined</Button>
              <Button variant="text" color="primary">Text</Button>
              <Button variant="contained" color="success">Success</Button>
              <Button variant="contained" color="warning">Warning</Button>
              <Button variant="contained" color="error">Error</Button>
            </Box>
          </Box>

          {/* Text Field */}
          <Box sx={{ mb: 3 }}>
            <Typography variant="h6" gutterBottom>Text Fields</Typography>
            <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
              <TextField label="Standard" variant="outlined" />
              <TextField label="Filled" variant="filled" />
              <TextField label="Standard" variant="standard" />
            </Box>
          </Box>

          {/* Chips */}
          <Box sx={{ mb: 3 }}>
            <Typography variant="h6" gutterBottom>Chips</Typography>
            <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
              <Chip label="Default" />
              <Chip label="Primary" color="primary" />
              <Chip label="Secondary" color="secondary" />
              <Chip label="Success" color="success" />
              <Chip label="Warning" color="warning" />
              <Chip label="Error" color="error" />
            </Box>
          </Box>
        </CardContent>
      </Card>

      {/* List Example */}
      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Typography variant="h5" gutterBottom>
            List Components
          </Typography>
          <List>
            <ListItemButton>
              <ListItemText primary="List Item 1" secondary="Secondary text" />
            </ListItemButton>
            <ListItemButton>
              <ListItemText primary="List Item 2" secondary="Secondary text" />
            </ListItemButton>
            <ListItemButton selected>
              <ListItemText primary="Selected Item" secondary="This item is selected" />
            </ListItemButton>
          </List>
        </CardContent>
      </Card>

      {/* App Bar Example */}
      <Card>
        <CardContent>
          <Typography variant="h5" gutterBottom>
            App Bar Example
          </Typography>
          <AppBar position="static" sx={{ borderRadius: 1 }}>
            <Toolbar>
              <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
                Sample App Bar
              </Typography>
              <Button color="inherit">Login</Button>
            </Toolbar>
          </AppBar>
        </CardContent>
      </Card>
    </Container>
  )
}

export default ThemeTestPage
