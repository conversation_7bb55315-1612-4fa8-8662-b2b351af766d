import React from 'react'
import <PERSON>act<PERSON><PERSON> from 'react-dom/client'
import App from './App.jsx'
import './index.css'

// Global error handlers for better debugging
window.addEventListener('unhandledrejection', (event) => {
  console.error('Unhandled promise rejection:', event.reason)

  // Don't prevent default for auth errors - they're expected
  if (event.reason?.response?.status === 401) {
    console.log('Authentication error caught globally - this is expected when not logged in')
    event.preventDefault() // Prevent the error from being logged as uncaught
  }

  // Suppress browser extension errors
  if (event.reason?.message?.includes('Could not establish connection') ||
      event.reason?.message?.includes('Receiving end does not exist')) {
    console.log('Browser extension error suppressed:', event.reason.message)
    event.preventDefault()
  }
})

window.addEventListener('error', (event) => {
  // Suppress browser extension errors
  if (event.message?.includes('Could not establish connection') ||
      event.message?.includes('Receiving end does not exist') ||
      event.filename?.includes('extension')) {
    console.log('Browser extension error suppressed:', event.message)
    event.preventDefault()
    return
  }

  console.error('Global error:', event.error)
})

// Suppress browser extension polyfill errors
window.addEventListener('error', (event) => {
  if (event.filename && event.filename.includes('polyfill.js')) {
    event.preventDefault()
    return false
  }
})

window.addEventListener('unhandledrejection', (event) => {
  if (event.reason && event.reason.message &&
      event.reason.message.includes('Could not establish connection')) {
    event.preventDefault()
    return false
  }
})

ReactDOM.createRoot(document.getElementById('root')).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>,
)
