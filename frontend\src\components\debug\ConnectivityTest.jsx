import React, { useState, useEffect } from 'react'
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Chip,
  CircularProgress,
  Alert,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider
} from '@mui/material'
import {
  CheckCircle,
  Error,
  Warning,
  Refresh,
  Computer,
  Security,
  NetworkCheck
} from '@mui/icons-material'
import { runHealthChecks } from '../../utils/healthCheck'

const ConnectivityTest = () => {
  const [results, setResults] = useState(null)
  const [loading, setLoading] = useState(false)

  const runTests = async () => {
    setLoading(true)
    try {
      const healthResults = await runHealthChecks()
      setResults(healthResults)
    } catch (error) {
      console.error('Failed to run health checks:', error)
      setResults({
        backend: {
          success: false,
          message: 'Failed to run health checks',
          error: error.message
        },
        auth: null
      })
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    runTests()
  }, [])

  const getStatusIcon = (success) => {
    if (success === null) return <Warning color="warning" />
    return success ? <CheckCircle color="success" /> : <Error color="error" />
  }

  const getStatusColor = (success) => {
    if (success === null) return 'warning'
    return success ? 'success' : 'error'
  }

  const getStatusText = (success) => {
    if (success === null) return 'Not Tested'
    return success ? 'Healthy' : 'Failed'
  }

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
        <Typography variant="h4">
          Connectivity Test
        </Typography>
        <Button
          variant="contained"
          startIcon={loading ? <CircularProgress size={20} /> : <Refresh />}
          onClick={runTests}
          disabled={loading}
        >
          {loading ? 'Testing...' : 'Run Tests'}
        </Button>
      </Box>

      {results && (
        <Box sx={{ display: 'grid', gap: 3 }}>
          {/* Backend Health */}
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Computer sx={{ mr: 1 }} />
                <Typography variant="h6">Backend Server</Typography>
                <Chip
                  label={getStatusText(results.backend.success)}
                  color={getStatusColor(results.backend.success)}
                  size="small"
                  sx={{ ml: 'auto' }}
                />
              </Box>
              
              <Alert 
                severity={results.backend.success ? 'success' : 'error'}
                sx={{ mb: 2 }}
              >
                {results.backend.message}
              </Alert>

              {results.backend.data && (
                <List dense>
                  <ListItem>
                    <ListItemIcon>
                      <NetworkCheck />
                    </ListItemIcon>
                    <ListItemText
                      primary="Server Status"
                      secondary={results.backend.data.message}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon>
                      <Computer />
                    </ListItemIcon>
                    <ListItemText
                      primary="Port"
                      secondary={results.backend.data.port || 'Unknown'}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon>
                      <CheckCircle />
                    </ListItemIcon>
                    <ListItemText
                      primary="Uptime"
                      secondary={`${Math.round(results.backend.data.uptime)} seconds`}
                    />
                  </ListItem>
                </List>
              )}

              {results.backend.error && (
                <Alert severity="error" sx={{ mt: 2 }}>
                  <Typography variant="subtitle2">Error Details:</Typography>
                  <Typography variant="body2">{results.backend.error}</Typography>
                </Alert>
              )}
            </CardContent>
          </Card>

          {/* Authentication Health */}
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Security sx={{ mr: 1 }} />
                <Typography variant="h6">Authentication</Typography>
                <Chip
                  label={getStatusText(results.auth?.success)}
                  color={getStatusColor(results.auth?.success)}
                  size="small"
                  sx={{ ml: 'auto' }}
                />
              </Box>

              {results.auth ? (
                <>
                  <Alert 
                    severity={results.auth.success ? 'success' : 'error'}
                    sx={{ mb: 2 }}
                  >
                    {results.auth.message}
                  </Alert>

                  {results.auth.data && (
                    <List dense>
                      <ListItem>
                        <ListItemIcon>
                          <Security />
                        </ListItemIcon>
                        <ListItemText
                          primary="User ID"
                          secondary={results.auth.data.user?.id || 'Unknown'}
                        />
                      </ListItem>
                      <ListItem>
                        <ListItemIcon>
                          <CheckCircle />
                        </ListItemIcon>
                        <ListItemText
                          primary="Username"
                          secondary={results.auth.data.user?.username || 'Unknown'}
                        />
                      </ListItem>
                    </List>
                  )}

                  {results.auth.error && (
                    <Alert severity="error" sx={{ mt: 2 }}>
                      <Typography variant="subtitle2">Error Details:</Typography>
                      <Typography variant="body2">{results.auth.error}</Typography>
                    </Alert>
                  )}
                </>
              ) : (
                <Alert severity="warning">
                  Authentication test skipped because backend is not accessible
                </Alert>
              )}
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Quick Actions
              </Typography>
              <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                <Button
                  variant="outlined"
                  onClick={() => window.open('http://localhost:10001/api/health', '_blank')}
                >
                  Open Backend Health Check
                </Button>
                <Button
                  variant="outlined"
                  onClick={() => console.log('Current localStorage:', localStorage)}
                >
                  Log LocalStorage
                </Button>
                <Button
                  variant="outlined"
                  onClick={() => {
                    localStorage.removeItem('token')
                    localStorage.removeItem('refreshToken')
                    window.location.reload()
                  }}
                >
                  Clear Auth & Reload
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Box>
      )}
    </Box>
  )
}

export default ConnectivityTest
