import React, { useState } from 'react'
import {
  IconButton,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Tooltip,
  CircularProgress,
  useTheme as useMuiTheme
} from '@mui/material'
import {
  DarkMode,
  LightMode,
  SettingsBrightness,
  Check
} from '@mui/icons-material'
import { useTheme } from '../../context/ThemeContext'
import { THEME_MODES } from '../../utils/constants'

const ThemeToggle = ({ variant = 'icon', size = 'medium', showLabel = false }) => {
  const muiTheme = useMuiTheme()
  const { themeMode, toggleTheme, setTheme, loading, isDark, isLight, isSystem } = useTheme()
  const [anchorEl, setAnchorEl] = useState(null)
  const open = Boolean(anchorEl)

  const handleClick = (event) => {
    if (variant === 'menu') {
      setAnchorEl(event.currentTarget)
    } else {
      toggleTheme()
    }
  }

  const handleClose = () => {
    setAnchorEl(null)
  }

  const handleThemeSelect = (mode) => {
    setTheme(mode)
    handleClose()
  }

  const getIcon = () => {
    if (loading) {
      return <CircularProgress size={20} />
    }
    
    switch (themeMode) {
      case THEME_MODES.DARK:
        return <DarkMode />
      case THEME_MODES.LIGHT:
        return <LightMode />
      case THEME_MODES.SYSTEM:
        return <SettingsBrightness />
      default:
        return <LightMode />
    }
  }

  const getTooltip = () => {
    switch (themeMode) {
      case THEME_MODES.DARK:
        return 'Switch to light mode'
      case THEME_MODES.LIGHT:
        return 'Switch to dark mode'
      case THEME_MODES.SYSTEM:
        return 'Using system theme'
      default:
        return 'Toggle theme'
    }
  }

  if (variant === 'menu') {
    return (
      <>
        <Tooltip title="Theme settings">
          <IconButton
            onClick={handleClick}
            size={size}
            color="inherit"
            disabled={loading}
          >
            {getIcon()}
          </IconButton>
        </Tooltip>
        
        <Menu
          anchorEl={anchorEl}
          open={open}
          onClose={handleClose}
          transformOrigin={{ horizontal: 'right', vertical: 'top' }}
          anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
          PaperProps={{
            sx: {
              mt: 1,
              minWidth: 180,
              '& .MuiMenuItem-root': {
                px: 2,
                py: 1
              }
            }
          }}
        >
          <MenuItem
            onClick={() => handleThemeSelect(THEME_MODES.LIGHT)}
            selected={isLight && !isSystem}
          >
            <ListItemIcon>
              <LightMode fontSize="small" />
              {isLight && !isSystem && <Check fontSize="small" sx={{ ml: 1 }} />}
            </ListItemIcon>
            <ListItemText>Light</ListItemText>
          </MenuItem>
          
          <MenuItem
            onClick={() => handleThemeSelect(THEME_MODES.DARK)}
            selected={isDark && !isSystem}
          >
            <ListItemIcon>
              <DarkMode fontSize="small" />
              {isDark && !isSystem && <Check fontSize="small" sx={{ ml: 1 }} />}
            </ListItemIcon>
            <ListItemText>Dark</ListItemText>
          </MenuItem>
          
          <MenuItem
            onClick={() => handleThemeSelect(THEME_MODES.SYSTEM)}
            selected={isSystem}
          >
            <ListItemIcon>
              <SettingsBrightness fontSize="small" />
              {isSystem && <Check fontSize="small" sx={{ ml: 1 }} />}
            </ListItemIcon>
            <ListItemText>System</ListItemText>
          </MenuItem>
        </Menu>
      </>
    )
  }

  return (
    <Tooltip title={getTooltip()}>
      <IconButton
        onClick={handleClick}
        size={size}
        color="inherit"
        disabled={loading}
        sx={{
          transition: 'transform 0.2s ease-in-out',
          '&:hover': {
            transform: 'scale(1.1)'
          }
        }}
      >
        {getIcon()}
      </IconButton>
    </Tooltip>
  )
}

export default ThemeToggle
