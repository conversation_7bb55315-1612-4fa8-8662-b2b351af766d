const express = require('express');
const router = express.Router();
const { protect } = require('../middleware/auth');

// Health check route (no auth required)
router.get('/health', (req, res) => {
  res.status(200).json({
    success: true,
    message: 'Backend server is running',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    version: process.version
  });
});

// Debug route to test authentication
router.get('/auth-test', protect, (req, res) => {
  res.status(200).json({
    success: true,
    message: 'Authentication working correctly',
    user: {
      id: req.user._id,
      username: req.user.username,
      email: req.user.email,
      role: req.user.role
    },
    timestamp: new Date().toISOString()
  });
});

// Debug route to test without authentication
router.get('/no-auth-test', (req, res) => {
  res.status(200).json({
    success: true,
    message: 'No auth route working',
    timestamp: new Date().toISOString()
  });
});

module.exports = router;
