const jwt = require('jsonwebtoken');
const User = require('../models/User');

// Protect routes
exports.protect = async (req, res, next) => {
  let token;

  // Check if token exists in headers
  if (
    req.headers.authorization &&
    req.headers.authorization.startsWith('Bearer')
  ) {
    // Set token from Bear<PERSON> token in header
    token = req.headers.authorization.split(' ')[1];
  } else if (req.cookies && req.cookies.token) {
    // Set token from cookie
    token = req.cookies.token;
  }

  // Make sure token exists
  if (!token) {
    console.log('Auth middleware: No token provided for route:', req.originalUrl);
    return res.status(401).json({
      success: false,
      error: 'Not authorized to access this route',
    });
  }

  try {
    // Verify token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);

    if (!decoded || !decoded.id) {
      return res.status(401).json({
        success: false,
        error: 'Invalid token. Please log in again.',
      });
    }

    // Get user from the token
    const user = await User.findById(decoded.id);

    if (!user) {
      console.log('Auth middleware: User not found for ID:', decoded.id);
      return res.status(401).json({
        success: false,
        error: 'User not found. Please log in again.',
      });
    }

    // Set user in request object
    req.user = user;
    console.log('Auth middleware: User authenticated successfully:', user.username, 'for route:', req.originalUrl);

    next();
  } catch (error) {
    console.error('Auth middleware error:', error);

    return res.status(401).json({
      success: false,
      error: 'Authentication failed. Please log in again.',
    });
  }
};

// Optional authentication - sets req.user if token exists but doesn't block if no token
exports.optionalAuth = async (req, res, next) => {
  let token;

  // Check if token exists in headers
  if (
    req.headers.authorization &&
    req.headers.authorization.startsWith('Bearer')
  ) {
    // Set token from Bearer token in header
    token = req.headers.authorization.split(' ')[1];
  } else if (req.cookies && req.cookies.token) {
    // Set token from cookie
    token = req.cookies.token;
  }

  // If no token, just continue without setting user
  if (!token) {
    return next();
  }

  try {
    // Verify token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);

    if (decoded && decoded.id) {
      // Get user from the token
      const user = await User.findById(decoded.id);

      // Set user in request object if found
      if (user) {
        req.user = user;
      }
    }

    // Continue regardless of whether user was found
    next();
  } catch (error) {
    // If token is invalid, just continue without setting user
    console.log('Optional auth: Invalid token, continuing without user');
    next();
  }
};

// Grant access to specific roles
exports.authorize = (...roles) => {
  return (req, res, next) => {
    // If no roles are specified, allow all roles
    if (roles.length === 0) {
      return next();
    }

    // Check if user has a role property
    if (!req.user || !req.user.role) {
      return res.status(403).json({
        success: false,
        error: 'User role not defined',
      });
    }

    // Check if user's role is in the allowed roles
    if (!roles.includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        error: `User role ${req.user.role} is not authorized to access this route`,
      });
    }

    next();
  };
};
