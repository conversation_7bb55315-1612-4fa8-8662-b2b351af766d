import axios from './fixedAxios'

/**
 * Check if the backend server is running and accessible
 * @returns {Promise<{success: boolean, message: string, data?: any}>}
 */
export const checkBackendHealth = async () => {
  try {
    console.log('Checking backend health...')
    const response = await axios.get('/api/health', {
      timeout: 5000 // Short timeout for health check
    })
    
    console.log('Backend health check successful:', response.data)
    return {
      success: true,
      message: 'Backend is running',
      data: response.data
    }
  } catch (error) {
    console.error('Backend health check failed:', error.message)
    
    let message = 'Backend is not accessible'
    if (error.code === 'ECONNREFUSED') {
      message = 'Cannot connect to backend server. Please check if it\'s running on the correct port.'
    } else if (error.message.includes('timeout')) {
      message = 'Backend server is not responding (timeout)'
    } else if (error.response) {
      message = `Backend returned error: ${error.response.status} ${error.response.statusText}`
    }
    
    return {
      success: false,
      message,
      error: error.message
    }
  }
}

/**
 * Test authentication with the backend
 * @returns {Promise<{success: boolean, message: string, data?: any}>}
 */
export const checkAuthHealth = async () => {
  try {
    console.log('Checking authentication health...')
    const response = await axios.get('/api/debug/auth-test')
    
    console.log('Auth health check successful:', response.data)
    return {
      success: true,
      message: 'Authentication is working',
      data: response.data
    }
  } catch (error) {
    console.error('Auth health check failed:', error.message)
    
    let message = 'Authentication failed'
    if (error.response?.status === 401) {
      message = 'Not authenticated - please log in'
    } else if (error.response?.status === 403) {
      message = 'Access denied'
    } else if (error.code === 'ECONNREFUSED') {
      message = 'Cannot connect to backend server'
    }
    
    return {
      success: false,
      message,
      error: error.message
    }
  }
}

/**
 * Run comprehensive health checks
 * @returns {Promise<{backend: object, auth: object}>}
 */
export const runHealthChecks = async () => {
  console.log('Running comprehensive health checks...')
  
  const results = {
    backend: await checkBackendHealth(),
    auth: null
  }
  
  // Only check auth if backend is healthy
  if (results.backend.success) {
    results.auth = await checkAuthHealth()
  }
  
  console.log('Health check results:', results)
  return results
}

export default {
  checkBackendHealth,
  checkAuthHealth,
  runHealthChecks
}
