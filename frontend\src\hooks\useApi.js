import { useState, useEffect, useCallback } from 'react'
import { useSnackbar } from 'notistack'
import axios from '../utils/fixedAxios'

const useApi = (url, options = {}) => {
  const [data, setData] = useState(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)
  const { enqueueSnackbar } = useSnackbar()

  const {
    immediate = true,
    method = 'GET',
    onSuccess,
    onError,
    showErrorSnackbar = true,
    showSuccessSnackbar = false,
    successMessage = 'Operation completed successfully',
    transform = (data) => data
  } = options

  const execute = useCallback(async (customUrl, customOptions = {}) => {
    try {
      setLoading(true)
      setError(null)

      const requestUrl = customUrl || url
      const requestOptions = {
        method,
        url: requestUrl,
        ...customOptions
      }

      const response = await axios(requestOptions)
      const transformedData = transform(response.data)

      setData(transformedData)

      if (showSuccessSnackbar) {
        enqueueSnackbar(successMessage, { variant: 'success' })
      }

      onSuccess?.(transformedData, response)
      return transformedData

    } catch (err) {
      const errorMessage = err.response?.data?.message || err.message || 'An error occurred'
      setError(errorMessage)

      // Don't show snackbar for authentication errors unless explicitly requested
      if (showErrorSnackbar && !(err.response?.status === 401 && !showErrorSnackbar)) {
        // Only show auth errors if user is expected to be authenticated
        if (err.response?.status === 401) {
          console.log('Authentication error in useApi:', errorMessage)
        } else {
          enqueueSnackbar(errorMessage, { variant: 'error' })
        }
      }

      onError?.(err)
      throw err

    } finally {
      setLoading(false)
    }
  }, [url, method, enqueueSnackbar]) // Remove unstable dependencies

  const refetch = useCallback(() => {
    return execute()
  }, [execute])

  const reset = useCallback(() => {
    setData(null)
    setError(null)
    setLoading(false)
  }, [])

  useEffect(() => {
    if (immediate && url) {
      execute()
    }
  }, [immediate, url]) // Remove execute from dependencies to prevent infinite loop

  return {
    data,
    loading,
    error,
    execute,
    refetch,
    reset
  }
}

export default useApi
