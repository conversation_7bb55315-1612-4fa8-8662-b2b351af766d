import { Suspense, lazy } from 'react'
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import CssBaseline from '@mui/material/CssBaseline'
import { SnackbarProvider } from 'notistack'
// import { QueryClientProvider } from '@tanstack/react-query'
// import { ReactQueryDevtools } from '@tanstack/react-query-devtools'

// Import contexts
import { AuthProvider } from './context/AuthContext'
import { SocketProvider } from './context/SocketContext'
import { NotificationProvider } from './context/NotificationContext'
import { SettingsProvider } from './context/SettingsContext'
import { ThemeProvider } from './context/ThemeContext'

// Import components
import ProtectedRoute from './components/ProtectedRoute'
import Layout from './components/Layout'
import { ErrorBoundary, LoadingSpinner } from './components/ui'
// import { queryClient } from './lib/react-query'
// import { usePWA } from './hooks/usePWA'

// Lazy load pages for code splitting
const AuthPage = lazy(() => import('./pages/AuthPage'))
const HomePage = lazy(() => import('./pages/HomePage'))
const ProfilePage = lazy(() => import('./pages/ProfilePage'))
const MessagesPage = lazy(() => import('./pages/MessagesPage'))
const GalleryPage = lazy(() => import('./pages/GalleryPage'))
const CreatePost = lazy(() => import('./pages/CreatePost'))
const SettingsPage = lazy(() => import('./pages/SettingsPage'))

// Lazy load additional pages
const NotificationsPage = lazy(() => import('./pages/NotificationsPage'))
const SearchPage = lazy(() => import('./pages/SearchPage'))
const LivePage = lazy(() => import('./pages/LivePage'))
const ShopPage = lazy(() => import('./pages/ShopPage'))
const TrendingPage = lazy(() => import('./pages/TrendingPage'))

// Debug components
const SettingsDebug = lazy(() => import('./components/debug/SettingsDebug'))
const ThemeTestPage = lazy(() => import('./pages/ThemeTestPage'))
const ConnectivityTest = lazy(() => import('./components/debug/ConnectivityTest'))

// PWA Install Component (temporarily disabled)
// const PWAInstallPrompt = () => {
//   const { canInstall, installApp } = usePWA()

//   if (!canInstall) return null

//   return (
//     <div style={{
//       position: 'fixed',
//       bottom: 20,
//       left: 20,
//       right: 20,
//       background: 'linear-gradient(45deg, #1976d2, #e91e63)',
//       color: 'white',
//       padding: '16px',
//       borderRadius: '12px',
//       display: 'flex',
//       alignItems: 'center',
//       justifyContent: 'space-between',
//       zIndex: 1000,
//       boxShadow: '0 4px 16px rgba(0,0,0,0.2)'
//     }}>
//       <div>
//         <strong>Install Let's Talk</strong>
//         <div style={{ fontSize: '0.875rem', opacity: 0.9 }}>
//           Get the full app experience!
//         </div>
//       </div>
//       <button
//         onClick={installApp}
//         style={{
//           background: 'rgba(255,255,255,0.2)',
//           border: 'none',
//           color: 'white',
//           padding: '8px 16px',
//           borderRadius: '8px',
//           cursor: 'pointer',
//           fontWeight: 600
//         }}
//       >
//         Install
//       </button>
//     </div>
//   )
// }

function App() {
  // Page loading component
  const PageLoader = () => (
    <LoadingSpinner
      fullScreen
      message="Loading page..."
      size={60}
    />
  )

  return (
    <ErrorBoundary fallbackMessage="Something went wrong with the application. Please refresh the page.">
      <AuthProvider>
        <SettingsProvider>
          <ThemeProvider>
            <CssBaseline />
            <SnackbarProvider
              maxSnack={3}
              anchorOrigin={{
                vertical: 'bottom',
                horizontal: 'right',
              }}
              dense
            >
              <SocketProvider>
                <NotificationProvider>
                  <Router>
                <Suspense fallback={<PageLoader />}>
                  <Routes>
                      {/* Public routes */}
                      <Route path="/auth" element={<AuthPage />} />

                      {/* Protected routes */}
                      <Route path="/" element={
                        <ProtectedRoute>
                          <Layout>
                            <HomePage />
                          </Layout>
                        </ProtectedRoute>
                      } />

                      <Route path="/profile/:id?" element={
                        <ProtectedRoute>
                          <Layout>
                            <ProfilePage />
                          </Layout>
                        </ProtectedRoute>
                      } />

                      <Route path="/messages" element={
                        <ProtectedRoute>
                          <Layout>
                            <MessagesPage />
                          </Layout>
                        </ProtectedRoute>
                      } />

                      <Route path="/gallery" element={
                        <ProtectedRoute>
                          <Layout>
                            <GalleryPage />
                          </Layout>
                        </ProtectedRoute>
                      } />

                      <Route path="/create" element={
                        <ProtectedRoute>
                          <Layout>
                            <CreatePost />
                          </Layout>
                        </ProtectedRoute>
                      } />

                      <Route path="/settings" element={
                        <ProtectedRoute>
                          <Layout>
                            <SettingsPage />
                          </Layout>
                        </ProtectedRoute>
                      } />

                      <Route path="/notifications" element={
                        <ProtectedRoute>
                          <Layout>
                            <NotificationsPage />
                          </Layout>
                        </ProtectedRoute>
                      } />

                      <Route path="/search" element={
                        <ProtectedRoute>
                          <Layout>
                            <SearchPage />
                          </Layout>
                        </ProtectedRoute>
                      } />

                      <Route path="/live" element={
                        <ProtectedRoute>
                          <Layout>
                            <LivePage />
                          </Layout>
                        </ProtectedRoute>
                      } />

                      <Route path="/shop" element={
                        <ProtectedRoute>
                          <Layout>
                            <ShopPage />
                          </Layout>
                        </ProtectedRoute>
                      } />

                      <Route path="/trending" element={
                        <ProtectedRoute>
                          <Layout>
                            <TrendingPage />
                          </Layout>
                        </ProtectedRoute>
                      } />

                      {/* Debug routes - only in development */}
                      {process.env.NODE_ENV === 'development' && (
                        <>
                          <Route path="/debug/settings" element={
                            <ProtectedRoute>
                              <Layout>
                                <SettingsDebug />
                              </Layout>
                            </ProtectedRoute>
                          } />
                          <Route path="/debug/theme" element={
                            <ProtectedRoute>
                              <Layout>
                                <ThemeTestPage />
                              </Layout>
                            </ProtectedRoute>
                          } />
                          <Route path="/debug/connectivity" element={
                            <ProtectedRoute>
                              <Layout>
                                <ConnectivityTest />
                              </Layout>
                            </ProtectedRoute>
                          } />
                        </>
                      )}
                    </Routes>
                  </Suspense>

                  {/* PWA Install Prompt - temporarily disabled */}
                  {/* <PWAInstallPrompt /> */}
                  </Router>
                </NotificationProvider>
              </SocketProvider>
            </SnackbarProvider>
          </ThemeProvider>
        </SettingsProvider>
      </AuthProvider>

      {/* React Query Devtools - temporarily disabled */}
      {/* {process.env.NODE_ENV === 'development' && (
        <ReactQueryDevtools initialIsOpen={false} />
      )} */}
    </ErrorBoundary>
  )
}

export default App
