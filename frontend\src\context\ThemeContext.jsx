import React, { createContext, useContext, useState, useEffect } from 'react'
import { ThemeProvider as MuiThemeProvider } from '@mui/material/styles'
import { lightTheme, darkTheme } from '../styles/theme'
import { STORAGE_KEYS, THEME_MODES } from '../utils/constants'
import { useAuth } from './AuthContext'
import axios from '../utils/fixedAxios'

const ThemeContext = createContext()

export const useTheme = () => {
  const context = useContext(ThemeContext)
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider')
  }
  return context
}

export const ThemeProvider = ({ children }) => {
  const { user, isAuthenticated } = useAuth()
  const [themeMode, setThemeMode] = useState(() => {
    const saved = localStorage.getItem(STORAGE_KEYS.THEME)
    return saved || THEME_MODES.LIGHT
  })
  const [loading, setLoading] = useState(false)

  // Get the actual theme object
  const theme = themeMode === THEME_MODES.DARK ? darkTheme : lightTheme

  // Detect system theme preference
  const getSystemTheme = () => {
    if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
      return THEME_MODES.DARK
    }
    return THEME_MODES.LIGHT
  }

  // Apply theme to document
  useEffect(() => {
    const actualMode = themeMode === THEME_MODES.SYSTEM ? getSystemTheme() : themeMode
    document.documentElement.setAttribute('data-theme', actualMode)
    document.body.style.backgroundColor = actualMode === THEME_MODES.DARK ? '#121212' : '#fafafa'
    document.body.style.color = actualMode === THEME_MODES.DARK ? '#ffffff' : '#212121'
  }, [themeMode])

  // Listen for system theme changes
  useEffect(() => {
    if (themeMode === THEME_MODES.SYSTEM) {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
      const handleChange = () => {
        const systemTheme = getSystemTheme()
        document.documentElement.setAttribute('data-theme', systemTheme)
        document.body.style.backgroundColor = systemTheme === THEME_MODES.DARK ? '#121212' : '#fafafa'
        document.body.style.color = systemTheme === THEME_MODES.DARK ? '#ffffff' : '#212121'
      }

      mediaQuery.addEventListener('change', handleChange)
      return () => mediaQuery.removeEventListener('change', handleChange)
    }
  }, [themeMode])

  // Save theme preference locally
  useEffect(() => {
    localStorage.setItem(STORAGE_KEYS.THEME, themeMode)
  }, [themeMode])

  // Sync with backend when user is authenticated
  useEffect(() => {
    if (isAuthenticated && user && user.id) {
      console.log('User authenticated, syncing theme with backend')
      syncThemeWithBackend()
    } else {
      console.log('User not authenticated or missing user data, skipping theme sync')
    }
  }, [isAuthenticated, user?.id]) // Only depend on user.id to avoid unnecessary calls

  const syncThemeWithBackend = async () => {
    // Only sync if user is authenticated
    if (!isAuthenticated || !user) {
      console.log('User not authenticated, skipping theme sync')
      return
    }

    try {
      setLoading(true)
      console.log('Syncing theme with backend for user:', user.id)

      // Get user's theme preference from backend
      const response = await axios.get('/api/users/me/theme')

      if (response.data.success && response.data.data) {
        const backendTheme = response.data.data.mode || 'light'
        console.log('Backend theme preference:', backendTheme)

        if (backendTheme !== themeMode) {
          console.log(`Updating theme from ${themeMode} to ${backendTheme}`)
          setThemeMode(backendTheme)
        }
      }
    } catch (error) {
      console.warn('Failed to sync theme with backend:', {
        status: error.response?.status,
        message: error.response?.data?.message || error.message,
        authenticated: isAuthenticated
      })

      // If it's a 403/401 error, don't retry
      if (error.response?.status === 403 || error.response?.status === 401) {
        console.log('Authentication error, will not retry theme sync')
      }
    } finally {
      setLoading(false)
    }
  }

  const updateThemePreference = async (newMode) => {
    const previousMode = themeMode

    try {
      setLoading(true)
      setThemeMode(newMode)

      console.log(`Updating theme preference to: ${newMode}`)

      // Update backend if user is authenticated
      if (isAuthenticated && user) {
        console.log('Updating theme preference on backend')
        await axios.put('/api/users/me/theme', {
          mode: newMode
        })
        console.log('Theme preference updated successfully on backend')
      } else {
        console.log('User not authenticated, theme saved locally only')
      }
    } catch (error) {
      console.error('Failed to update theme preference:', {
        status: error.response?.status,
        message: error.response?.data?.message || error.message,
        authenticated: isAuthenticated
      })

      // Revert on error
      console.log(`Reverting theme from ${newMode} back to ${previousMode}`)
      setThemeMode(previousMode)

      // Don't show error for auth issues
      if (error.response?.status !== 403 && error.response?.status !== 401) {
        console.error('Non-auth error updating theme:', error)
      }
    } finally {
      setLoading(false)
    }
  }

  const toggleTheme = () => {
    const newMode = themeMode === THEME_MODES.DARK ? THEME_MODES.LIGHT : THEME_MODES.DARK
    updateThemePreference(newMode)
  }

  const setSystemTheme = () => {
    updateThemePreference(THEME_MODES.SYSTEM)
  }

  // Debug function for testing
  const testThemeSync = async () => {
    console.log('Testing theme sync...')
    console.log('Current state:', {
      isAuthenticated,
      userId: user?.id,
      themeMode,
      loading
    })

    if (isAuthenticated && user) {
      await syncThemeWithBackend()
    } else {
      console.log('Cannot test theme sync - user not authenticated')
    }
  }

  // Expose test function to window for debugging
  React.useEffect(() => {
    window.testThemeSync = testThemeSync
    return () => {
      delete window.testThemeSync
    }
  }, [isAuthenticated, user, themeMode])

  const value = {
    themeMode,
    theme,
    loading,
    toggleTheme,
    setTheme: updateThemePreference,
    setSystemTheme,
    isDark: themeMode === THEME_MODES.DARK || (themeMode === THEME_MODES.SYSTEM && getSystemTheme() === THEME_MODES.DARK),
    isLight: themeMode === THEME_MODES.LIGHT || (themeMode === THEME_MODES.SYSTEM && getSystemTheme() === THEME_MODES.LIGHT),
    isSystem: themeMode === THEME_MODES.SYSTEM,
    actualTheme: themeMode === THEME_MODES.SYSTEM ? getSystemTheme() : themeMode
  }

  return (
    <ThemeContext.Provider value={value}>
      <MuiThemeProvider theme={theme}>
        {children}
      </MuiThemeProvider>
    </ThemeContext.Provider>
  )
}

export default ThemeProvider
