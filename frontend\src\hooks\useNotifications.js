import { useState, useEffect, useCallback } from 'react'
import { useSnackbar } from 'notistack'
import useApi from './useApi'
import { useSocket } from '../context/SocketContext'
import { useAuth } from '../context/AuthContext'

const useNotifications = () => {
  const [notifications, setNotifications] = useState([])
  const [unreadCount, setUnreadCount] = useState(0)
  const [toastNotification, setToastNotification] = useState(null)
  const [showToast, setShowToast] = useState(false)
  const { enqueueSnackbar } = useSnackbar()
  const { socket } = useSocket()
  const { isAuthenticated, loading: authLoading } = useAuth()

  // Fetch notifications only when authenticated
  const {
    data: notificationsResponse,
    loading: notificationsLoading,
    error: notificationsError,
    refetch: refetchNotifications,
    execute: executeNotificationsFetch
  } = useApi('/api/notifications', {
    immediate: false, // Don't fetch immediately
    transform: (response) => response,
    showErrorSnackbar: false // Don't show error snackbar for auth errors
  })

  // Fetch notifications when user becomes authenticated
  useEffect(() => {
    if (isAuthenticated && !authLoading) {
      console.log('User authenticated, fetching notifications...')
      executeNotificationsFetch()
    } else if (!isAuthenticated && !authLoading) {
      console.log('User not authenticated, clearing notifications...')
      setNotifications([])
      setUnreadCount(0)
    }
  }, [isAuthenticated, authLoading, executeNotificationsFetch])

  // Update local state when data changes
  useEffect(() => {
    if (notificationsResponse?.data) {
      setNotifications(notificationsResponse.data)
      setUnreadCount(notificationsResponse.unreadCount || 0)
    }
  }, [notificationsResponse])

  // Handle notification errors
  useEffect(() => {
    if (notificationsError && isAuthenticated) {
      console.error('Error fetching notifications:', notificationsError)
      // Only show error if it's not an auth error
      if (!notificationsError.includes('401') && !notificationsError.includes('Unauthorized')) {
        enqueueSnackbar('Failed to load notifications', { variant: 'error' })
      }
    }
  }, [notificationsError, isAuthenticated, enqueueSnackbar])

  // Socket listeners for real-time notifications
  useEffect(() => {
    if (!socket) return

    const handleNewNotification = (notification) => {
      console.log('New notification received:', notification)

      // Add to notifications list
      setNotifications(prev => [notification, ...prev])
      setUnreadCount(prev => prev + 1)

      // Show toast notification
      setToastNotification(notification)
      setShowToast(true)

      // Also show snackbar as fallback
      enqueueSnackbar(notification.title || notification.message, {
        variant: 'info',
        autoHideDuration: 4000,
      })
    }

    const handleNotificationRead = (data) => {
      if (data.allRead) {
        // Mark all as read
        setNotifications(prev => prev.map(n => ({ ...n, read: true })))
        setUnreadCount(0)
      } else if (data.notificationId) {
        // Mark specific notification as read
        setNotifications(prev =>
          prev.map(n =>
            n.id === data.notificationId ? { ...n, read: true } : n
          )
        )
        setUnreadCount(prev => Math.max(0, prev - 1))
      }
    }

    const handleNotificationDeleted = (data) => {
      setNotifications(prev => prev.filter(n => n.id !== data.notificationId))
      // Update unread count if deleted notification was unread
      const deletedNotification = notifications.find(n => n.id === data.notificationId)
      if (deletedNotification && !deletedNotification.read) {
        setUnreadCount(prev => Math.max(0, prev - 1))
      }
    }

    // Socket event listeners
    socket.on('notification', handleNewNotification)
    socket.on('notification_read', handleNotificationRead)
    socket.on('notification_deleted', handleNotificationDeleted)

    return () => {
      socket.off('notification', handleNewNotification)
      socket.off('notification_read', handleNotificationRead)
      socket.off('notification_deleted', handleNotificationDeleted)
    }
  }, [socket, enqueueSnackbar, notifications])

  // Mark notification as read
  const markAsRead = useCallback(async (notificationId) => {
    try {
      await useApi(`/api/notifications/${notificationId}/read`, {
        method: 'PUT',
        immediate: false
      }).execute()

      setNotifications(prev =>
        prev.map(n =>
          n.id === notificationId ? { ...n, read: true } : n
        )
      )
      setUnreadCount(prev => Math.max(0, prev - 1))

    } catch (error) {
      enqueueSnackbar('Failed to mark notification as read', { variant: 'error' })
    }
  }, [enqueueSnackbar])

  // Mark all notifications as read
  const markAllAsRead = useCallback(async () => {
    try {
      await useApi('/api/notifications/mark-all-read', {
        method: 'PUT',
        immediate: false
      }).execute()

      setNotifications(prev =>
        prev.map(n => ({ ...n, read: true }))
      )
      setUnreadCount(0)

      enqueueSnackbar('All notifications marked as read', { variant: 'success' })

    } catch (error) {
      enqueueSnackbar('Failed to mark all notifications as read', { variant: 'error' })
    }
  }, [enqueueSnackbar])

  // Delete notification
  const deleteNotification = useCallback(async (notificationId) => {
    try {
      await useApi(`/api/notifications/${notificationId}`, {
        method: 'DELETE',
        immediate: false
      }).execute()

      setNotifications(prev => prev.filter(n => n.id !== notificationId))

      // Update unread count if the deleted notification was unread
      const deletedNotification = notifications.find(n => n.id === notificationId)
      if (deletedNotification && !deletedNotification.read) {
        setUnreadCount(prev => Math.max(0, prev - 1))
      }

      enqueueSnackbar('Notification deleted', { variant: 'success' })

    } catch (error) {
      enqueueSnackbar('Failed to delete notification', { variant: 'error' })
    }
  }, [notifications, enqueueSnackbar])

  // Clear all notifications
  const clearAll = useCallback(async () => {
    try {
      await useApi('/api/notifications/clear-all', {
        method: 'DELETE',
        immediate: false
      }).execute()

      setNotifications([])
      setUnreadCount(0)

      enqueueSnackbar('All notifications cleared', { variant: 'success' })

    } catch (error) {
      enqueueSnackbar('Failed to clear notifications', { variant: 'error' })
    }
  }, [enqueueSnackbar])

  // Get notifications by type
  const getNotificationsByType = useCallback((type) => {
    return notifications.filter(n => n.type === type)
  }, [notifications])

  // Get unread notifications
  const getUnreadNotifications = useCallback(() => {
    return notifications.filter(n => !n.read)
  }, [notifications])

  // Close toast notification
  const closeToast = useCallback(() => {
    setShowToast(false)
    setTimeout(() => setToastNotification(null), 150)
  }, [])

  return {
    notifications,
    unreadCount,
    loading: notificationsLoading,
    error: notificationsError,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    clearAll,
    refetch: refetchNotifications,
    getNotificationsByType,
    getUnreadNotifications,
    // Toast notification state
    toastNotification,
    showToast,
    closeToast
  }
}

export default useNotifications
